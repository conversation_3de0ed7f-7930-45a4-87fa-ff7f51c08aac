<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>
    <style>
        /* test formatu plików */
        @page {
            margin:0;
        }
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url('{{ storage_path('fonts/Roboto-Regular.ttf') }}') format('truetype');
        }

        @font-face {
            font-family: 'Roboto';
            font-style: medium;
            font-weight: 500;
            src: url('{{ storage_path('fonts/Roboto-Medium.ttf') }}') format('truetype');
        }

        @font-face {
            font-family: 'Roboto';
            font-style: bold;
            font-weight: 700;
            src: url('{{ storage_path('fonts/Roboto-Bold.ttf') }}') format('truetype');
        }

        @font-face {
            font-family: 'Roboto';
            font-style: black;
            font-weight: 900;
            src: url('{{ storage_path('fonts/Roboto-Black.ttf') }}') format('truetype');
        }

        body {
            background-color: #FFFFFF;
            font-family: Roboto, sans-serif;
            /*font-family: DejaVu Sans, sans-serif;*/
            font-size: 9pt;
            text-align: center;
        }


        #kontener {
            position: absolute;
            border: 1px solid #C1A500;
            margin: 60px auto auto;
            padding: 20px 0px 20px 0px;
            background-color: #FFE9BC;
            width: 900px;
        }

        #lewy {
            text-align: left;
        }

        #prawy {
            text-align: right;
        }

        #lewy h3 {
            margin-left: 3mm;
        }

        .data {
            font-size: 12px;
            font-style: normal;
            color: #000000;
            text-align: right;
            vertical-align: middle;
        }

        .dane {
            font-size: 12px;
            font-style: normal;
            text-align: left;
            border: 0px solid Black;
            border-collapse: collapse;
            border-spacing: 0px;
        }

        .dane td {
            padding-bottom: 0.05cm;
        }

        .pozycje {
            font-size: 11px;
            font-style: normal;
            text-align: left;
            border: 1px solid Black;
            border-collapse: collapse;
            /*border-spacing: 5px;*/
        }

        .pozycje td {
            padding: 1mm;
            border: 1px solid black;
        }

        .pozycje th {
            padding: 0.1cm;
            border: 1px solid black;
            text-align: center;
        }

        .prefooter {
            font-size: 12px;
            font-style: normal;
            text-align: left;
            border: 0px solid Black;
            border-collapse: collapse;
            border-spacing: 0px;
        }

        .prefooter td {
            padding: 0px;
            border: 0px solid black;
        }

        .prefooter .vat {
            font-size: 12px;
            font-style: normal;
            text-align: left;
            border: 1px solid Black;
            border-collapse: collapse;
            border-spacing: 5px;
            padding: inherit;
        }

        .prefooter .vat td {
            padding: 0.1cm;
            border: 1px solid black;
        }

        .tresctabeli {
            font-size: 10pt;
        }

        .opisf {
            font-size: 12px;
        }

        .opisf td {
            padding-bottom: 0.1cm;
        }

        .invoice-box {
            max-width: 190mm;
            height: 290mm;
            margin: auto;
            padding-top: 5mm;
            background-color: #fff;
            display: block;
            position: relative;
        }

        .footer {
            position: absolute;
            width: 180mm;
            height: 7mm;
            left: 20mm;
            bottom: 8mm;
        }
    </style>
</head>

<body>
<div class="invoice-box">
@yield('content')
</div>
<div class="footer">
    <div class="footer-line">
        <div class="underlined">
            <span>TwojeFaktury.EU - {{date('Y')}}</span>
        </div>
    </div>
</div>
</body>
</html>
