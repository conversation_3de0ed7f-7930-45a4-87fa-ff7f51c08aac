<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Current Subscription Section --}}
        <div class="bg-white dark:bg-gray-900 shadow rounded-lg">
            {{ $this->currentSubscriptionInfolist }}
        </div>

        {{-- Available Plans Section --}}
        <div class="bg-white dark:bg-gray-900 shadow rounded-lg p-6">
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Dostępne plany
                </h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Wybierz plan, który najlepiej odpowiada Twoim potrzebom
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-6">
                @foreach($this->getAvailablePlansData() as $plan)
                    <div class="relative flex flex-col h-full border border-gray-200 dark:border-gray-700 rounded-lg p-6
                        {{ $plan['is_current'] ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-900/20' : 'hover:border-gray-300 dark:hover:border-gray-600' }}">

                        {{-- Plan Header --}}
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $plan['name'] }}
                            </h3>
                            <div class="mt-2">
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ $plan['price'] }}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    / {{ $plan['period'] }}
                                </span>
                            </div>
                            <div class="mt-2">
                            @if($plan['type'])
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $plan['type'] === 'Trial' ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' }}">
                                    {{ $plan['type'] }}
                                </span>
                            @endif
                            {{-- Current Plan Badge --}}
                            @if($plan['is_current'])
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                    Aktualny plan
                                </span>
                            @endif
                                @if($this->pendingSubscription && $this->pendingSubscription->plan_id === $plan['id'])
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                                        Oczekuje na aktywację
                                    </span>
                                @endif
                            </div>
                        </div>

                        {{-- Plan Description --}}
                        <div class="flex-grow"
                        @if($plan['description'])
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                {{ $plan['description'] }}
                            </p>
                        @endif

                        {{-- Plan Features --}}
                        @if(!empty($plan['features']))
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                                    Funkcje:
                                </h4>
                                <ul class="space-y-1">
                                    @foreach($plan['features'] as $feature)
                                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                            <x-heroicon-o-check class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                            {{ $feature }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                        {{-- Action Button --}}
                        <div class="mt-auto">
                            @if($plan['is_current'])
                                <button disabled class="w-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md text-sm font-medium cursor-not-allowed">
                                    Aktualny plan
                                </button>
                            @elseif($plan['type'] === 'Trial' && $this->trialPlanUsed)
                                <button disabled class="w-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md text-sm font-medium cursor-not-allowed">
                                    Plan próbny wykorzystany
                                </button>
                            @elseif($this->pendingSubscription)
                                <button disabled class="w-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md text-sm font-medium cursor-not-allowed">
                                    Oczekuje na aktywację
                                </button>
                            @elseif($this->currentSubscription && $this->currentSubscription->plan->weight > $plan['weight'])
                                <button disabled class="w-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-md text-sm font-medium cursor-not-allowed">
                                    Niedostępny
                                </button>
                            @else
                                {{ ($this->createNewSubscription)(['planId' => $plan['id']]) }}
                            @endif
                        </div>
                    </div>
                @endforeach
                <x-filament-actions::modals />
            </div>
        </div>

        {{-- Subscription History Section --}}
{{--        @if($this->currentSubscription)--}}
{{--            <div class="bg-white dark:bg-gray-900 shadow rounded-lg p-6">--}}
{{--                <div class="mb-4">--}}
{{--                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">--}}
{{--                        Historia subskrypcji--}}
{{--                    </h2>--}}
{{--                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">--}}
{{--                        Informacje o Twojej subskrypcji i płatnościach--}}
{{--                    </p>--}}
{{--                </div>--}}

{{--                <div class="overflow-hidden">--}}
{{--                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">--}}
{{--                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">--}}
{{--                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">--}}
{{--                                ID subskrypcji--}}
{{--                            </div>--}}
{{--                            <div class="mt-1 text-sm text-gray-900 dark:text-white font-mono">--}}
{{--                                {{ $this->currentSubscription->getOrderId() }}--}}
{{--                            </div>--}}
{{--                        </div>--}}

{{--                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">--}}
{{--                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">--}}
{{--                                Data utworzenia--}}
{{--                            </div>--}}
{{--                            <div class="mt-1 text-sm text-gray-900 dark:text-white">--}}
{{--                                {{ $this->currentSubscription->created_at->format('d.m.Y H:i') }}--}}
{{--                            </div>--}}
{{--                        </div>--}}

{{--                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">--}}
{{--                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">--}}
{{--                                Ostatnia aktualizacja--}}
{{--                            </div>--}}
{{--                            <div class="mt-1 text-sm text-gray-900 dark:text-white">--}}
{{--                                {{ $this->currentSubscription->updated_at->format('d.m.Y H:i') }}--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        @endif--}}

        {{-- Help Section --}}
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div class="flex items-start">
                <x-heroicon-o-information-circle class="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Potrzebujesz pomocy?
                    </h3>
                    <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                        Jeśli masz pytania dotyczące subskrypcji lub potrzebujesz pomocy przy wyborze planu,
                        skontaktuj się z naszym zespołem wsparcia.
                    </p>
                    <div class="mt-3">
                        <x-filament::button
                            tag="a"
                            href="{{ \App\Filament\App\Pages\Contact::getUrl() }}"
                            color="primary"
                            size="sm"
                            outlined
                        >
                            Skontaktuj się z obsługą
                        </x-filament::button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
