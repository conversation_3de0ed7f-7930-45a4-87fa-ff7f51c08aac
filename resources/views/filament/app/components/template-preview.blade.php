@if($template)
<div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
    <div class="flex items-start justify-between mb-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ $template['template_name'] }}</h3>
            <p class="text-sm text-gray-600 mt-1">{{ $template['description'] }}</p>
        </div>
        <div class="text-right">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Wersja {{ $template['version'] }}
            </span>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-4 mt-4">
        <div>
            <h4 class="text-sm font-medium text-gray-900 mb-2">Obsługiwane pola</h4>
            <div class="flex flex-wrap gap-1">
                @foreach($template['supported_fields'] as $field)
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        {{ $field }}
                    </span>
                @endforeach
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 gap-4 mt-4">
        <div>
            <h4 class="text-sm font-medium text-gray-900 mb-2">Miejsce na logo</h4>
            <div class="flex flex-wrap gap-1">
                @if($template['has_logo'])
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                        TAK
                    </span>
                    @if(blank(tenantLogo(tenant())))
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                            <a href="{{ \App\Filament\App\Pages\CompanyProfile::getUrl() }}" class="text-blue-600 hover:underline">
                                Dodaj logo firmy
                            </a>
                        </span>
                    @endif
                @else
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                        NIE
                    </span>
                @endif
            </div>
        </div>
    </div>
</div>
@else
<div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
    <div class="text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Brak wybranego szablonu</h3>
        <p class="mt-1 text-sm text-gray-500">Wybierz szablon z listy powyżej, aby zobaczyć jego szczegóły.</p>
    </div>
</div>
@endif
