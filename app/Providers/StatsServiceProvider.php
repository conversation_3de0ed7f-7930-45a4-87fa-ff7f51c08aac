<?php

namespace App\Providers;

use App\Services\StatsService;
use Illuminate\Support\ServiceProvider;

class StatsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('stats', function ($app) {
            return new StatsService();
        });

        $this->app->singleton(StatsService::class, function ($app) {
            return $app['stats'];
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/stats.php' => config_path('stats.php'),
            ], 'stats-config');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return ['stats', StatsService::class];
    }
}
