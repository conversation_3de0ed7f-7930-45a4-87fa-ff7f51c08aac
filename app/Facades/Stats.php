<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * Statistics Facade
 * 
 * @method static int increment(string $eventName, int $amount = 1, ?\Carbon\Carbon $date = null)
 * @method static int getCount(string $eventName, ?\Carbon\Carbon $date = null)
 * @method static array getDailyStats(?\Carbon\Carbon $date = null)
 * @method static int aggregateDailyStats(?\Carbon\Carbon $date = null)
 * @method static int clearDailyRedisStats(?\Carbon\Carbon $date = null)
 * @method static \Illuminate\Support\Collection getHistoricalStats(string $eventName, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
 * @method static int getTotalCount(string $eventName, \Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
 * 
 * @see \App\Services\StatsService
 */
class Stats extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'stats';
    }
}
