<?php

namespace App\Listeners;

use App\Enums\PlanType;
use App\Facades\Stats;
use App\Jobs\PaymentCompletedJob;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class PaymentCompletedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        if ($event->payment->subscription->plan->type === PlanType::TRIAL) {
            Notification::make()
                ->title('Twój plan próbny jest aktywny')
                ->body('Dziękujemy za rejestrację w naszym serwisie. Twój plan próbny jest już aktywny.')
                ->success()
                ->sendToDatabase($event->payment->user);
            return;
        }

        Notification::make()
            ->title('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> została zakończona')
            ->body('Dziękujemy za dokonanie płatno<PERSON>')
            ->success()
            ->sendToDatabase($event->payment->user);

        Mail::to($event->payment->user->email)
            ->queue(new \App\Mail\PaymentSuccess($event->payment));

        Stats::increment('payment.completed');
    }
}
