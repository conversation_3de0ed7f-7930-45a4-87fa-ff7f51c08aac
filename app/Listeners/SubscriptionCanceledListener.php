<?php

namespace App\Listeners;

use App\Facades\Stats;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SubscriptionCanceledListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Mail::to($event->subscription->user->email)
            ->queue(new \App\Mail\SubscriptionCanceled($event->subscription));

        Stats::increment('subscription.canceled');
    }
}
