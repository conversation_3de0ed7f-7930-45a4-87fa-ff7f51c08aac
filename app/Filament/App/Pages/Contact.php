<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;

class Contact extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-phone';

    protected static string $view = 'filament.app.pages.contact';

    protected static ?string $slug = 'contact';

    protected static ?string $navigationLabel = 'Kontakt';
    protected ?string $heading = 'Formularz kontaktowy';

    protected static ?int $navigationSort = 100;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isTenantAdmin();
    }
}
