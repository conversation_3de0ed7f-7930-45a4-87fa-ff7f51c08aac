<?php

namespace App\Filament\Resources\TenantResource\Pages;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Filament\App\Resources\PartnerResource;
use App\Filament\Resources\TenantResource;
use App\Helpers\StringHelper;
use App\Repositories\GUSRepository;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Model;

class CreateTenant extends CreateRecord
{
    protected static string $resource = TenantResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Dane podstawowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\Textarea::make('name')
                            ->label('Nazwa')
                            ->required()
                            ->columnSpanFull()
                            ->maxLength(120),
                        Forms\Components\TextInput::make('postcode')
                            ->label('Kod pocztowy')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('city')
                            ->label('Miasto')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('phone')
                            ->label('Telefon')
                            ->maxLength(100),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->maxLength(60),
                        Forms\Components\TextInput::make('contact_name')
                            ->label('Osoba konaktowa'),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->label('Strona www'),
                        Forms\Components\Textarea::make('address')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
                Forms\Components\Section::make('Dane księgowe')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('vat_id')
                            ->label('NIP'),
                        Select::make('tax_residency_country')
                            ->label(__('app.partners.create.tax_residency_country'))
                            ->options(
                                TaxResidencyCountries::toArrayWithLabels()
                            )
                            ->formatStateUsing(fn($state) => $state ?? TaxResidencyCountries::PL->name),
                        Select::make('business_type')
                            ->label(__('app.partners.create.business_type'))
                            ->options(PartnerBusinessTypes::toArrayWithLabels())
                            ->default(PartnerBusinessTypes::INDIVIDUAL->value),
                        Select::make('vat_type')
                            ->label(__('app.partners.create.vat_type'))
                            ->options(PartnerVATTypes::toArrayWithLabels())
                            ->default(PartnerVATTypes::LOCAL->value),
                        Select::make('tax_type')
                            ->label(__('app.partners.create.tax_type'))
                            ->options(TaxTypePL::toArrayWithLabels())
                            ->default(TaxTypePL::LINEAR->value),
                        Select::make('accounting_type')
                            ->label(__('app.partners.create.accounting_type'))
                            ->options(AccountingTypesPL::toArrayWithLabels())
                            ->default(AccountingTypesPL::FULL->value),
                    ]),
                Forms\Components\Section::make('Dane systemowe')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktywny')
                            ->required(),
                        Forms\Components\TextInput::make('system_domain')
                            ->url()
                            ->label('Domena w systemie'),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('gus-import')
                ->label('Importuj z GUS')
                ->modalHeading('Importuj partnera z GUS')
                ->modalWidth(MaxWidth::Medium)
                ->form([
                    Forms\Components\TextInput::make('nip')
                        ->label('Wpisz NIP')
                        ->helperText('Tylko 10 cyfr!')
                        ->required()
                ])
                ->modalSubmitAction(fn (Actions\StaticAction $action) => $action->label('Importuj'))
                ->action(function (array $data) {
                    $nip = StringHelper::extractDigits($data['nip']);
                    $company = GUSRepository::findByNip($nip);
                    if (blank($company)) {
                        Notification::make()->title('Partner z podanego NIP nie został znaleziony')
                            ->danger()
                            ->send();
                        return;
                    }
                    $data = PartnerResource::mapGUSResponseToFormData($company);
                    $this->form->fill(array_merge($this->data, $data));
                }),
        ];
    }

    protected function handleRecordCreation(array $data): Model
    {
        if (empty($data['hash'])) {
            $data['hash'] = bin2hex(random_bytes(16));
        }
        return parent::handleRecordCreation($data);
    }
}
