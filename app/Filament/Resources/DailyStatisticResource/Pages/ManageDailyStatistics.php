<?php

namespace App\Filament\Resources\DailyStatisticResource\Pages;

use App\Filament\Resources\DailyStatisticResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageDailyStatistics extends ManageRecords
{
    protected static string $resource = DailyStatisticResource::class;

    protected function getHeaderActions(): array
    {
        return [
//            Actions\CreateAction::make(),
        ];
    }
}
