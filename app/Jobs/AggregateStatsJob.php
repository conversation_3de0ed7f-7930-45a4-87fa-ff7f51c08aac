<?php

namespace App\Jobs;

use App\Services\StatsService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AggregateStatsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Carbon $date;
    protected bool $clearRedisAfterAggregation;

    /**
     * Create a new job instance.
     */
    public function __construct(?Carbon $date = null, bool $clearRedisAfterAggregation = true)
    {
        $this->date = $date ?? Carbon::yesterday();
        $this->clearRedisAfterAggregation = $clearRedisAfterAggregation;
        
        // Set queue from config
        $this->onQueue(config('stats.aggregation.queue', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(StatsService $statsService): void
    {
        try {
            Log::info("[Stats] Starting aggregation job for {$this->date->format('Y-m-d')}");

            // Aggregate statistics from Redis to database
            $aggregatedCount = $statsService->aggregateDailyStats($this->date);

            // Clear Redis keys if configured to do so
            if ($this->clearRedisAfterAggregation && config('stats.aggregation.clear_redis_after_aggregation', true)) {
                $clearedCount = $statsService->clearDailyRedisStats($this->date);
                Log::info("[Stats] Cleared {$clearedCount} Redis keys for {$this->date->format('Y-m-d')}");
            }

            Log::info("[Stats] Successfully aggregated {$aggregatedCount} statistics for {$this->date->format('Y-m-d')}");

        } catch (\Exception $e) {
            Log::error("[Stats] Failed to aggregate statistics for {$this->date->format('Y-m-d')}: " . $e->getMessage());
            
            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("[Stats] Aggregation job failed for {$this->date->format('Y-m-d')}: " . $exception->getMessage());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['stats', 'aggregation', $this->date->format('Y-m-d')];
    }
}
