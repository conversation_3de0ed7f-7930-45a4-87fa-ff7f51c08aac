<?php

namespace App\Console\Commands;

use App\Enums\SubscriptionStatus;
use App\Filament\App\Pages\SubscriptionManagement;
use App\Mail\SubscriptionEnding1d;
use App\Mail\SubscriptionEnding7d;
use App\Models\Subscription;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class NotifySubscriptionEnding1d extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:notify-ending-1d {--dry-run : Preview subscriptions without sending emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email notifications to users whose subscriptions end tomorrow';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $targetDate = Carbon::now()->addDays(1)->format('Y-m-d');

        $this->info("Looking for subscriptions ending on: {$targetDate}");

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No emails will be sent');
        }

        // Query subscriptions ending in exactly 1 days
        $subscriptions = Subscription::with(['user', 'plan'])
            ->where('status', SubscriptionStatus::ACTIVE->name)
            ->whereDate('ends_at', $targetDate)
            ->get();

        $totalFound = $subscriptions->count();
        $this->info("Found {$totalFound} subscription(s) ending tomorrow");

        if ($totalFound === 0) {
            $this->info('No subscriptions found. Exiting.');
            return self::SUCCESS;
        }

        // Display preview information
        if ($isDryRun || $this->option('verbose')) {
            $this->displaySubscriptionPreview($subscriptions);
        }

        if ($isDryRun) {
            $this->info('Dry run completed. No emails were sent.');
            return self::SUCCESS;
        }

        // Send email notifications
        return $this->sendNotifications($subscriptions);
    }

    /**
     * Display preview of subscriptions that would be notified
     */
    private function displaySubscriptionPreview($subscriptions): void
    {
        $this->newLine();
        $this->info('Subscriptions to be notified:');

        $headers = ['ID', 'User Email', 'Plan', 'Ends At', 'Price'];
        $rows = [];

        foreach ($subscriptions as $subscription) {
            $rows[] = [
                $subscription->id,
                $subscription->user->email ?? 'N/A',
                $subscription->plan->name ?? 'N/A',
                $subscription->ends_at->format('Y-m-d'),
                $this->formatPrice($subscription->plan->price ?? 0)
            ];
        }

        $this->table($headers, $rows);
        $this->newLine();
    }

    /**
     * Send email notifications to users
     */
    private function sendNotifications($subscriptions): int
    {
        $successCount = 0;
        $failureCount = 0;
        $progressBar = $this->output->createProgressBar($subscriptions->count());

        $this->info('Sending email notifications...');
        $progressBar->start();

        foreach ($subscriptions as $subscription) {
            try {
                // Validate required data
                if (!$subscription->user || !$subscription->user->email) {
                    $this->error("Subscription ID {$subscription->id}: No user or email found");
                    $failureCount++;
                    $progressBar->advance();
                    continue;
                }

                if (!$subscription->plan) {
                    $this->error("Subscription ID {$subscription->id}: No plan found");
                    $failureCount++;
                    $progressBar->advance();
                    continue;
                }

                $nextSubPrice = $this->formatPrice($subscription->plan->price);

                Mail::to($subscription->user->email)
                    ->send(new SubscriptionEnding1d($subscription, $nextSubPrice));

                Notification::make()
                    ->title('Twoja subskrypcja kończy się jutro')
                    ->body('Przedłuż subskrypcję: ' . $subscription->plan->name)
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('view')
                            ->label('Zarządzaj subskrypcją')
                            ->button()
                            ->url(SubscriptionManagement::getUrl(), shouldOpenInNewTab: true),
                    ])
                    ->success()
                    ->sendToDatabase($subscription->user);

                $successCount++;
            } catch (\Exception $e) {
                $this->error("Failed to send email for subscription ID {$subscription->id}: " . $e->getMessage());
                $failureCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($successCount, $failureCount);

        return $failureCount > 0 ? self::FAILURE : self::SUCCESS;
    }

    /**
     * Display final results
     */
    private function displayResults(int $successCount, int $failureCount): void
    {
        $this->info('Email notification results:');
        $this->info("✅ Successfully sent: {$successCount}");

        if ($failureCount > 0) {
            $this->error("❌ Failed to send: {$failureCount}");
        } else {
            $this->info("❌ Failed to send: {$failureCount}");
        }

        $total = $successCount + $failureCount;
        $this->info("📊 Total processed: {$total}");

        if ($failureCount > 0) {
            $this->warn('Some emails failed to send. Check the error messages above for details.');
        } else {
            $this->info('All notifications sent successfully! 🎉');
        }
    }

    /**
     * Format price for display
     */
    private function formatPrice($price): string
    {
        if (is_object($price) && method_exists($price, '__toString')) {
            return (string) $price;
        }

        // Fallback formatting if price is numeric
        if (is_numeric($price)) {
            return number_format($price, 2, ',', ' ') . ' zł';
        }

        return 'N/A';
    }
}
