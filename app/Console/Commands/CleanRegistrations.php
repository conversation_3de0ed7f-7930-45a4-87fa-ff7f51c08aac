<?php

namespace App\Console\Commands;

use App\Models\Registration;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanRegistrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'registration:clean
        {--dry-run : Show what would be deleted without actually deleting}
        {--no-confirm : Don\'t ask for confirmation}
        {--force : Force deletion without any prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean old registration records based on their status and age (uses app.registration config values)';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting registration cleanup...');

        $stats = $this->getCleanupStats();

        if ($stats['total'] === 0) {
            $this->info('No registrations found for cleanup.');
            return self::SUCCESS;
        }

        $this->displayStats($stats);

        if ($this->option('dry-run')) {
            $this->info('Dry run completed. No records were deleted.');
            return self::SUCCESS;
        }

        if (!$this->option('force') && !$this->confirmDeletion($stats)) {
            $this->info('Cleanup cancelled.');
            return self::SUCCESS;
        }

        $deleted = $this->performCleanup();

        $this->info("Cleanup completed. Deleted {$deleted} registration records.");
        Log::info("Registration cleanup completed. Deleted {$deleted} records.");

        return self::SUCCESS;
    }

    /**
     * Get statistics about registrations that would be cleaned up.
     */
    protected function getCleanupStats(): array
    {
        $now = now();
        $config = config('app.registration');

        // Unconfirmed registrations older than configured time
        $createdValidFor = $this->parseDuration($config['created_valid_for']);
        $unconfirmedCount = Registration::whereNull('confirmed_at')
            ->where('created_at', '<=', $now->copy()->sub($createdValidFor))
            ->count();

        // Confirmed but not finished registrations older than configured time from confirmation
        $confirmedValidFor = $this->parseDuration($config['confirmed_valid_for']);
        $confirmedNotFinishedCount = Registration::whereNotNull('confirmed_at')
            ->whereNull('finished_at')
            ->where('confirmed_at', '<=', $now->copy()->sub($confirmedValidFor))
            ->count();

        // Finished registrations older than configured time from finishing
        $finishedValidFor = $this->parseDuration($config['finished_valid_for']);
        $finishedCount = Registration::whereNotNull('finished_at')
            ->where('finished_at', '<=', $now->copy()->sub($finishedValidFor))
            ->count();

        return [
            'unconfirmed' => $unconfirmedCount,
            'confirmed_not_finished' => $confirmedNotFinishedCount,
            'finished' => $finishedCount,
            'total' => $unconfirmedCount + $confirmedNotFinishedCount + $finishedCount,
        ];
    }

    /**
     * Display cleanup statistics.
     */
    protected function displayStats(array $stats): void
    {
        $config = config('app.registration');

        $this->info('Registrations to be cleaned up:');
        $this->table(
            ['Type', 'Count', 'Rule'],
            [
                ['Unconfirmed', $stats['unconfirmed'], 'Older than ' . $this->formatDuration($config['created_valid_for']) . ' from creation'],
                ['Confirmed but not finished', $stats['confirmed_not_finished'], 'Older than ' . $this->formatDuration($config['confirmed_valid_for']) . ' from confirmation'],
                ['Finished', $stats['finished'], 'Older than ' . $this->formatDuration($config['finished_valid_for']) . ' from finishing'],
                ['Total', $stats['total'], ''],
            ]
        );
    }

    /**
     * Ask for confirmation before deletion.
     */
    protected function confirmDeletion(array $stats): bool
    {
        if ($this->option('no-confirm')) {
            return true;
        }

        return $this->confirm(
            "Are you sure you want to delete {$stats['total']} registration records?"
        );
    }

    /**
     * Perform the actual cleanup and return the number of deleted records.
     */
    protected function performCleanup(): int
    {
        $now = now();
        $config = config('app.registration');
        $totalDeleted = 0;

        // Delete unconfirmed registrations older than configured time
        $createdValidFor = $this->parseDuration($config['created_valid_for']);
        $deleted = Registration::whereNull('confirmed_at')
            ->where('created_at', '<=', $now->copy()->sub($createdValidFor))
            ->delete();
        $totalDeleted += $deleted;
        $this->info("Deleted {$deleted} unconfirmed registrations.");

        // Delete confirmed but not finished registrations older than configured time from confirmation
        $confirmedValidFor = $this->parseDuration($config['confirmed_valid_for']);
        $deleted = Registration::whereNotNull('confirmed_at')
            ->whereNull('finished_at')
            ->where('confirmed_at', '<=', $now->copy()->sub($confirmedValidFor))
            ->delete();
        $totalDeleted += $deleted;
        $this->info("Deleted {$deleted} confirmed but not finished registrations.");

        // Delete finished registrations older than configured time from finishing
        $finishedValidFor = $this->parseDuration($config['finished_valid_for']);
        $deleted = Registration::whereNotNull('finished_at')
            ->where('finished_at', '<=', $now->copy()->sub($finishedValidFor))
            ->delete();
        $totalDeleted += $deleted;
        $this->info("Deleted {$deleted} finished registrations.");

        return $totalDeleted;
    }

    /**
     * Parse ISO 8601 duration string to DateInterval.
     */
    protected function parseDuration(string $duration): \DateInterval
    {
        try {
            return new \DateInterval($duration);
        } catch (\Exception $e) {
            $this->error("Invalid duration format: {$duration}. Using default values.");

            // Fallback to default values based on the duration type
            if (str_contains($duration, 'M') && !str_contains($duration, 'D')) {
                return new \DateInterval('PT30M'); // 30 minutes
            }

            if (str_contains($duration, 'H')) {
                return new \DateInterval('PT1H'); // 1 hour
            }

            return new \DateInterval('P1D'); // 1 day
        }
    }

    /**
     * Format ISO 8601 duration string to human-readable format.
     */
    protected function formatDuration(string $duration): string
    {
        try {
            $interval = new \DateInterval($duration);
            $parts = [];

            if ($interval->d > 0) {
                $parts[] = $interval->d . ' day' . ($interval->d > 1 ? 's' : '');
            }
            if ($interval->h > 0) {
                $parts[] = $interval->h . ' hour' . ($interval->h > 1 ? 's' : '');
            }
            if ($interval->i > 0) {
                $parts[] = $interval->i . ' minute' . ($interval->i > 1 ? 's' : '');
            }

            return !empty($parts) ? implode(' ', $parts) : $duration;
        } catch (\Exception $e) {
            return $duration;
        }
    }
}
