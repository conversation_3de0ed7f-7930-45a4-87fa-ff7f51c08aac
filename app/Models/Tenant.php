<?php

namespace App\Models;

use App\Enums\AccountingTypesPL;
use App\Enums\PartnerBusinessTypes;
use App\Enums\PartnerVATTypes;
use App\Enums\PlanType;
use App\Enums\Roles;
use App\Enums\SubscriptionStatus;
use App\Enums\SystemModules;
use App\Enums\TaxResidencyCountries;
use App\Enums\TaxTypePL;
use App\Enums\WarehouseTypes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * Class Tenant
 *
 * @property int $id Primary key, auto-incremented
 * @property string $hash Unique hash identifier (64 characters)
 * @property string $name Name of the tenant (64 characters)
 * @property string|null $address Address of the tenant (TEXT)
 * @property string|null $postcode Postal code (100 characters)
 * @property string|null $city City name (100 characters)
 * @property string|null $phone Phone number (100 characters)
 * @property string|null $email Email address (60 characters)
 * @property string|null $contact_name Contact person's name (255 characters)
 * @property string|null $website Website URL (255 characters)
 * @property string|null $system_domain System domain of the tenant (255 characters)
 * @property string|null $vat_id VAT identification number (255 characters)
 * @property int|null|PartnerVATTypes $vat_type Type of VAT (smallint unsigned)
 * @property string|null|TaxResidencyCountries $tax_residency_country Tax residency country code (4 characters)
 * @property int|null $tax_type Type of tax (smallint unsigned)
 * @property int|null $accounting_type Accounting type identifier (smallint unsigned)
 * @property int|null $business_type Business type identifier (smallint unsigned)
 * @property bool $is_active Whether the tenant is active (0 = inactive, 1 = active)
 * @property string|null $created_at Timestamp of creation
 * @property string|null $updated_at Timestamp of last update
 * @property string|null $config Configuration settings (TEXT)
 */

class Tenant extends Model
{
    use HasFactory, SoftDeletes;


    protected static $unguarded = true;

    protected $casts = [
        'config' => 'array',
        'vat_type' => PartnerVATTypes::class,
        'business_type' => PartnerBusinessTypes::class,
        'tax_type' => TaxTypePL::class,
        'tax_residency_country' => TaxResidencyCountries::class,
        'accounting_type' => AccountingTypesPL::class,
        'is_active' => 'bool',
    ];

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function warehouse(): HasMany
    {
        return $this->hasMany(Warehouse::class, 'installation', 'id');
    }

    public function companyWarehouse(): HasMany
    {
        return $this->hasMany(Warehouse::class, 'installation', 'id')
            ->where('owner_type', WarehouseTypes::COMPANY->value);
    }

    public function warehouseDocs(): HasMany
    {
        return $this->hasMany(WarehouseDoc::class, 'installation', 'id');
    }

    public function partners(): HasMany
    {
        return $this->hasMany(Partner::class, 'installation', 'id');
    }

    public function systemPartner(): BelongsTo
    {
        return $this->belongsTo(Partner::class, 'system_partner', 'id');
    }

    public function getAddressText($format = 'text')
    {
        $lines = [];
        $lines[] = $this->name;
        $lines[] = $this->address;
        $lines[] = $this->postcode . ' ' . $this->city;
        $lines[] = $this->phone;
        $lines[] = $this->website . ' ' . $this->email;
        return match ($format) {
            'html' => implode('<br>', $lines),
            default => implode(PHP_EOL, $lines),
        };
    }

    public function getInvoiceText($format = 'text')
    {
        $lines = [];
        $lines[] = $this->name;
        $lines[] = $this->address;
        $lines[] = $this->postcode . ' ' . $this->city;
        $this->phone ? $lines[] = $this->phone : true;
        $lines[] = 'VAT: ' . $this->vat_id;
        return match ($format) {
            'html' => implode('<br>', $lines),
            default => implode(PHP_EOL, $lines),
        };
    }

    public function user(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'installations', 'tenant_id', 'user_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Products::class, 'installation', 'id');
    }

    public function hasModule(SystemModules $module): bool
    {
        return in_array($module->value, $this->config['modules'] ?? []);
    }

    public function admins(): BelongsToMany
    {
        return $this->user()->whereHas('roles', function (Builder $query) {
            $query->where('id', Roles::TENANT_ADMIN->value);
        });
    }

    public function meta(): HasOne
    {
        return $this->hasOne(TenantMeta::class, 'tenant_id', 'id');
    }

    public function getBankAccounts(): Collection
    {
        return collect($this->meta?->meta['bank_accounts'] ?? []);
    }

    public function getInvoiceConfiguration(): Collection
    {
        return collect(
            $this->meta?->meta['invoice_configuration'] ??
                ['selected_template' => config('app.default_invoice_template')]
        );
    }

    public function isActiveVATEntity(): bool
    {
        return $this->vat_type !== PartnerVATTypes::NOTVAT;
    }

    public function purchaseDocs(): HasMany
    {
        return $this->hasMany(PurchaseDoc::class, 'installation');
    }

    public function tradeDocs(): HasMany
    {
        return $this->hasMany(TradeDoc::class, 'installation');
    }

    public function docSeries(): HasMany
    {
        return $this->hasMany(DocumentSeriesPattern::class, 'installation');
    }

    public function jobTasks(): HasMany
    {
        return $this->hasMany(JobTask::class, 'installation');
    }

    public function productDemands(): HasMany
    {
        return $this->hasMany(ProductDemand::class, 'installation');
    }

    public function manufacturers(): HasMany
    {
        return $this->hasMany(Manufacturer::class, 'installation');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'tenant_id');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'tenant_id');
    }

    public function scopeActiveSubscriptions()
    {
        return $this->subscriptions()->where('status', SubscriptionStatus::ACTIVE->name);
    }

    public function getSubscriptionStatus(): ?string
    {
        return $this->config['subscription_status'] ?? null;
    }

    public function setSubscriptionStatus(SubscriptionStatus $status)
    {
        $this->config['subscription_status'] = $status->name;
        $this->save();
    }

    public function activateSubscription(Subscription $subscription): Tenant
    {
        if ($this->subscription_id !== null) {
            $oldsub = Subscription::find($this->subscription_id);
            if ($oldsub) {
                $oldsub->cancel();
            }
        }

        $tenantConfig = $this->config;

        $tenantConfig['modules'] = $subscription->plan->features ?? [
            SystemModules::INVOICES->value,
            SystemModules::SIMPLE_PRODUCTS->value
        ];
        $tenantConfig['selected_plan_id'] = $subscription->plan->id;
        $tenantConfig['subscription_status'] = $subscription->plan->type === PlanType::TRIAL ?
            SubscriptionStatus::TRIAL->name : SubscriptionStatus::ACTIVE->name;

        $this->update([
            'config' => $tenantConfig,
            'subscription_id' => $subscription->id
        ]);

        return $this->refresh();
    }

    public function cancelSubscription(): Tenant
    {
        $this->subscription->cancel();
        $config = $this->config;
        $config['subscription_status'] = SubscriptionStatus::CANCELED->name;
        $config['modules'] = [];
        $config['selected_plan_id'] = null;
        $this->config = $config;
        $this->subscription_id = null;
        $this->save();
        return $this->fresh();
    }

    public function expireSubscription(): Tenant
    {
        $this->subscription->makeExpired();
        $config = $this->config;
        $config['subscription_status'] = SubscriptionStatus::EXPIRED->name;
        $config['modules'] = [];
        $config['selected_plan_id'] = null;
        $this->config = $config;
        $this->subscription_id = null;
        $this->save();
        return $this->fresh();
    }

    public function getPendingSubscription(): ?Subscription
    {
        return $this->subscriptions()->where('status', SubscriptionStatus::PENDING->name)->first();
    }

    /**
     * Convert Tenant data to the Partner model
     *
     * @return Partner
     */
    public function convertToPartner(int $installation, bool $save = false): Partner
    {
        $partner = new Partner();

        // Direct field mappings
        $partner->installation = $installation;
        $partner->hash = $this->hash;
        $partner->name = $this->name;
        $partner->short_name = Str::limit($this->name, 110);
        $partner->address = $this->address ?? null;
        $partner->postcode = $this->postcode ?? null;
        $partner->city = $this->city ?? null;
        $partner->phone = $this->phone ?? null;
        $partner->email = $this->email ?? null;
        $partner->contact_name = $this->contact_name ?? null;
        $partner->website = $this->website ?? null;
        $partner->vat_id = $this->vat_id ?? null;
        $partner->vat_type = $this->vat_type ?? null;
        $partner->business_type = $this->business_type ?? null;
        $partner->tax_residency_country = $this->tax_residency_country?->name ?? TaxResidencyCountries::PL->name;
        $partner->country_id = $this->tax_residency_country?->name ?? TaxResidencyCountries::PL->name;
        $partner->is_active = $this->is_active;

        $bankAccounts = $this->getBankAccounts();
        if ($bankAccounts->count() > 0) {
            $partner->bank_name = $bankAccounts->first()['bank_name'];
            $partner->bank_iban = $bankAccounts->first()['bank_iban'];
            $partner->bank_swift = $bankAccounts->first()['bank_swift'];
            $partner->bank_account = $bankAccounts->first()['bank_account'];
        }

        if ($save) {
            $partner->save();
        }

        return $partner;
    }

    public function getMeta(): DTOTenantMetadata
    {
        return DTOTenantMetadata::make($this->meta?->meta ?? []);
    }
}
