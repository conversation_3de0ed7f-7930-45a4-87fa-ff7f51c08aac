<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * Daily Statistics Model
 *
 * @property int $id
 * @property \Carbon\Carbon $date
 * @property string $event_name
 * @property int $count
 * @property array|null $metadata
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class DailyStatistic extends Model
{
    use HasFactory;

    protected $fillable = [
        'date',
        'event_name',
        'count',
        'metadata',
    ];

    protected $casts = [
        'date' => 'date',
        'count' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
    }

    /**
     * Scope to filter by event name
     */
    public function scopeForEvent($query, string $eventName)
    {
        return $query->where('event_name', $eventName);
    }

    /**
     * Scope to filter by specific date
     */
    public function scopeForDate($query, Carbon $date)
    {
        return $query->where('date', $date->format('Y-m-d'));
    }

    public static function getEventStats(string $eventName, Carbon $startDate, Carbon $endDate): EloquentCollection
    {
        return static::forEvent($eventName)
            ->dateRange($startDate, $endDate)
            ->orderBy('date')
            ->get();
    }

    public static function getTotalCount(string $eventName, Carbon $startDate, Carbon $endDate): int
    {
        return static::forEvent($eventName)
            ->dateRange($startDate, $endDate)
            ->sum('count');
    }

    public static function getDateStats(Carbon $date): EloquentCollection
    {
        return static::forDate($date)
            ->orderBy('event_name')
            ->get();
    }
}
