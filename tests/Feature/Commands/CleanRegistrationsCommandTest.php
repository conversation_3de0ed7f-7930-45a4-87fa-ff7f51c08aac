<?php

namespace Tests\Feature\Commands;

use App\Models\Registration;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class CleanRegistrationsCommandTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        // Clean the registrations table before each test
        Registration::query()->delete();
    }

    public function test_command_shows_help_information()
    {
        $this->artisan('registration:clean --help')
            ->expectsOutputToContain('Clean old registration records based on their status and age')
            ->assertExitCode(0);
    }

    public function test_dry_run_shows_statistics_without_deleting()
    {
        // Create test registrations
        $this->createTestRegistrations();

        $this->artisan('registration:clean --dry-run')
            ->expectsOutput('Starting registration cleanup...')
            ->expectsOutput('Dry run completed. No records were deleted.')
            ->assertExitCode(0);

        // Verify no records were actually deleted
        $this->assertDatabaseCount('registrations', 4);
    }

    public function test_command_deletes_old_unconfirmed_registrations()
    {
        // Get the configured duration and add 1 minute to ensure it's old enough
        $configDuration = config('app.registration.created_valid_for');
        $interval = new \DateInterval($configDuration);
        $oldTime = now()->sub($interval)->subMinute();

        // Create unconfirmed registration older than configured time
        Registration::factory()->create([
            'confirmed_at' => null,
            'finished_at' => null,
            'created_at' => $oldTime,
        ]);

        // Create recent unconfirmed registration (should not be deleted)
        Registration::factory()->create([
            'confirmed_at' => null,
            'finished_at' => null,
            'created_at' => now()->subMinutes(5),
        ]);

        $this->artisan('registration:clean --force')
            ->assertExitCode(0);

        $this->assertDatabaseCount('registrations', 1);
    }

    public function test_command_deletes_old_confirmed_but_not_finished_registrations()
    {
        // Get the configured duration and add 1 minute to ensure it's old enough
        $configDuration = config('app.registration.confirmed_valid_for');
        $interval = new \DateInterval($configDuration);
        $oldTime = now()->sub($interval)->subMinute();

        // Create confirmed but not finished registration older than configured time
        Registration::factory()->create([
            'confirmed_at' => $oldTime,
            'finished_at' => null,
        ]);

        // Create recent confirmed but not finished registration (should not be deleted)
        Registration::factory()->create([
            'confirmed_at' => now()->subMinutes(30),
            'finished_at' => null,
        ]);

        $this->artisan('registration:clean --force')
            ->assertExitCode(0);

        $this->assertDatabaseCount('registrations', 1);
    }

    public function test_command_deletes_old_finished_registrations()
    {
        // Get the configured duration and add 1 hour to ensure it's old enough
        $configDuration = config('app.registration.finished_valid_for');
        $interval = new \DateInterval($configDuration);
        $oldTime = now()->sub($interval)->subHour();

        // Create finished registration older than configured time
        Registration::factory()->create([
            'confirmed_at' => $oldTime->copy()->subHour(),
            'finished_at' => $oldTime,
        ]);

        // Create recent finished registration (should not be deleted)
        Registration::factory()->create([
            'confirmed_at' => now()->subHours(2),
            'finished_at' => now()->subHours(1),
        ]);

        $this->artisan('registration:clean --force')
            ->assertExitCode(0);

        $this->assertDatabaseCount('registrations', 1);
    }

    public function test_command_handles_empty_table()
    {
        $this->artisan('registration:clean --dry-run')
            ->expectsOutput('No registrations found for cleanup.')
            ->assertExitCode(0);
    }

    private function createTestRegistrations(): void
    {
        $config = config('app.registration');

        // Old unconfirmed registration
        $createdInterval = new \DateInterval($config['created_valid_for']);
        Registration::factory()->create([
            'confirmed_at' => null,
            'finished_at' => null,
            'created_at' => now()->sub($createdInterval)->subMinute(),
        ]);

        // Old confirmed but not finished registration
        $confirmedInterval = new \DateInterval($config['confirmed_valid_for']);
        Registration::factory()->create([
            'confirmed_at' => now()->sub($confirmedInterval)->subMinute(),
            'finished_at' => null,
        ]);

        // Old finished registration
        $finishedInterval = new \DateInterval($config['finished_valid_for']);
        $oldFinishedTime = now()->sub($finishedInterval)->subHour();
        Registration::factory()->create([
            'confirmed_at' => $oldFinishedTime->copy()->subHour(),
            'finished_at' => $oldFinishedTime,
        ]);

        // Recent registration (should not be deleted)
        Registration::factory()->create([
            'confirmed_at' => null,
            'finished_at' => null,
            'created_at' => now()->subMinutes(10),
        ]);
    }
}
