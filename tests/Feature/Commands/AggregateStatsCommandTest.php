<?php

namespace Tests\Feature\Commands;

use App\Jobs\AggregateStatsJob;
use App\Models\DailyStatistic;
use App\Services\StatsService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;

class AggregateStatsCommandTest extends TestCase
{
    use DatabaseTransactions;

    private string $testKeyPrefix = 'test_stats';

    protected function setUp(): void
    {
        parent::setUp();

        // Override config for testing
        config(['stats.redis.key_prefix' => $this->testKeyPrefix]);
        config(['stats.logging.enabled' => false]);

        // Clear any existing test keys
        $this->clearTestRedisKeys();
    }

    protected function tearDown(): void
    {
        $this->clearTestRedisKeys();
        parent::tearDown();
    }

    private function clearTestRedisKeys(): void
    {
        $keys = Redis::keys("{$this->testKeyPrefix}:*");
        if (!empty($keys)) {
            Redis::del($keys);
        }
    }

    public function test_command_aggregates_stats_successfully(): void
    {
        $date = Carbon::createFromDate('2025', '01', '01');
        $statsService = new StatsService();

        // Add some test data to Redis
        $statsService->increment('event.one', 10, $date);
        $statsService->increment('event.two', 5, $date);

        // Run the command
        $this->artisan('stats:aggregate', ['date' => $date->format('Y-m-d')])
            ->expectsOutput("Processing statistics aggregation for {$date->format('Y-m-d')}")
            ->expectsOutput('Found 2 statistics in Redis for ' . $date->format('Y-m-d'))
            ->expectsOutput('Successfully aggregated 2 statistics to database.')
            ->assertExitCode(0);

        // Verify data is in database
        $dbStats = DailyStatistic::where('date', $date->format('Y-m-d'))->get();
        $this->assertCount(2, $dbStats);
    }

    public function test_command_with_queue_option(): void
    {
        Queue::fake();

        $date = Carbon::createFromDate('2025', '01', '01');

        $this->artisan('stats:aggregate', [
            'date' => $date->format('Y-m-d'),
            '--queue' => true
        ])
            ->expectsOutput("Processing statistics aggregation for {$date->format('Y-m-d')}")
            ->expectsOutput('Aggregation job dispatched to queue.')
            ->assertExitCode(0);

        Queue::assertPushed(AggregateStatsJob::class);
    }

    public function test_command_with_no_clear_option(): void
    {
        $date = Carbon::createFromDate('2025', '01', '01');
        $statsService = new StatsService();

        // Add some test data to Redis
        $statsService->increment('event.test', 5, $date);

        $this->artisan('stats:aggregate', [
            'date' => $date->format('Y-m-d'),
            '--no-clear' => true
        ])
            ->assertExitCode(0);

        // Verify Redis data still exists
        $redisStats = $statsService->getDailyStats($date);
        $this->assertNotEmpty($redisStats);
        $this->assertEquals(5, $redisStats['event.test']);
    }

    public function test_command_with_no_redis_data(): void
    {
        // Use a date far in the past to ensure no Redis data exists
        $date = Carbon::createFromFormat('Y-m-d', '2020-01-01');

        $this->artisan('stats:aggregate', ['date' => $date->format('Y-m-d')])
            ->expectsOutput("Processing statistics aggregation for {$date->format('Y-m-d')}")
            ->expectsOutput("No Redis statistics found for {$date->format('Y-m-d')}")
            ->assertExitCode(0);
    }

    public function test_command_with_existing_data_requires_confirmation(): void
    {
        $date = Carbon::createFromDate('2025', '01', '01');

        // Create existing database record
        DailyStatistic::create([
            'date' => $date->format('Y-m-d'),
            'event_name' => 'existing.event',
            'count' => 1,
        ]);

        $this->artisan('stats:aggregate', ['date' => $date->format('Y-m-d')])
            ->expectsQuestion(
                "Statistics for {$date->format('Y-m-d')} already exist (1 records). Continue anyway?",
                false
            )
            ->expectsOutput('Aggregation cancelled.')
            ->assertExitCode(0);
    }

    public function test_command_with_force_option_skips_confirmation(): void
    {
        $date = Carbon::createFromDate('2025', '01', '01');
        $statsService = new StatsService();
        $uniqueId = uniqid();

        // Create existing database record
        $existingEvent = "existing.event.{$uniqueId}";
        DailyStatistic::create([
            'date' => $date->format('Y-m-d'),
            'event_name' => $existingEvent,
            'count' => 1,
        ]);

        // Add Redis data
        $newEvent = "event.new.{$uniqueId}";
        $statsService->increment($newEvent, 3, $date);

        $this->artisan('stats:aggregate', [
            'date' => $date->format('Y-m-d'),
            '--force' => true
        ])
            ->expectsOutput("Processing statistics aggregation for {$date->format('Y-m-d')}")
            ->assertExitCode(0);

        // Should have both existing and new records for our unique events
        $dbStats = DailyStatistic::where('date', $date->format('Y-m-d'))
            ->where('event_name', 'like', "%.{$uniqueId}")
            ->get();
        $this->assertCount(2, $dbStats);

        // Verify the specific records exist
        $this->assertTrue($dbStats->contains('event_name', $existingEvent));
        $this->assertTrue($dbStats->contains('event_name', $newEvent));
    }

    public function test_command_with_invalid_date_format(): void
    {
        $this->artisan('stats:aggregate', ['date' => 'invalid-date'])
            ->expectsOutput('Invalid date format. Please use Y-m-d format.')
            ->assertExitCode(1);
    }

    public function test_command_defaults_to_yesterday(): void
    {
        $yesterday = Carbon::yesterday();
        $statsService = new StatsService();

        // Add some test data for yesterday
        $statsService->increment('event.yesterday', 1, $yesterday);

        $this->artisan('stats:aggregate')
            ->expectsOutput("Processing statistics aggregation for {$yesterday->format('Y-m-d')}")
            ->assertExitCode(0);
    }

    public function test_command_shows_summary_table(): void
    {
//        $date = Carbon::yesterday();
        $date = Carbon::createFromDate('2025', '01', '01');
        $statsService = new StatsService();

        // Add test data
        $statsService->increment('event.one', 5, $date);
        $statsService->increment('event.two', 3, $date);

        $this->artisan('stats:aggregate', ['date' => $date->format('Y-m-d')])
            ->expectsTable(
                ['Metric', 'Value'],
                [
                    ['Date', $date->format('Y-m-d')],
                    ['Redis Stats Found', '2'],
                    ['Database Records Created/Updated', '2'],
                    ['Redis Keys Cleared', '2'],
                ]
            )
            ->assertExitCode(0);
    }
}
