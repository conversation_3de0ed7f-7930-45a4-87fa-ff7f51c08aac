<?php

namespace Tests\Unit;

use App\Models\DailyStatistic;
use App\Services\StatsService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;

class StatsServiceTest extends TestCase
{
    use DatabaseTransactions;

    private StatsService $statsService;
    private string $testKeyPrefix = 'test_stats';

    protected function setUp(): void
    {
        parent::setUp();

        // Override config for testing
        config(['stats.redis.key_prefix' => $this->testKeyPrefix]);
        config(['stats.redis.ttl_days' => 1]);
        config(['stats.logging.enabled' => false]);

        $this->statsService = new StatsService();

        // Clear any existing test keys
        $this->clearTestRedisKeys();
    }

    protected function tearDown(): void
    {
        $this->clearTestRedisKeys();
        parent::tearDown();
    }

    private function clearTestRedisKeys(): void
    {
        // Use FLUSHDB to clear all keys in the test database
        Redis::connection('default')->flushdb();
    }

    public function test_can_increment_event_counter(): void
    {
        $eventName = 'test.event.increment.' . uniqid();
        $date = Carbon::today();

        $result = $this->statsService->increment($eventName);

        $this->assertEquals(1, $result);

        // Increment again
        $result = $this->statsService->increment($eventName);
        $this->assertEquals(2, $result);
        $this->clearTestRedisKeys();
    }

    public function test_can_increment_by_custom_amount(): void
    {
        $eventName = 'test.event.custom.' . uniqid();
        $amount = 5;

        $result = $this->statsService->increment($eventName, $amount);

        $this->assertEquals($amount, $result);
    }

    public function test_can_increment_for_specific_date(): void
    {
        $eventName = 'test.event.date.' . uniqid();
        $date = Carbon::yesterday();

        $result = $this->statsService->increment($eventName, 1, $date);

        $this->assertEquals(1, $result);

        // Verify it's stored with the correct date
        $count = $this->statsService->getCount($eventName, $date);
        $this->assertEquals(1, $count);
    }

    public function test_can_get_count_for_event(): void
    {
        $eventName = 'test.event.count.' . uniqid();

        // Initially should be 0
        $count = $this->statsService->getCount($eventName);
        $this->assertEquals(0, $count);

        // After increment should return correct count
        $this->statsService->increment($eventName, 3);
        $count = $this->statsService->getCount($eventName);
        $this->assertEquals(3, $count);
    }

    public function test_can_get_daily_stats(): void
    {
        $date = Carbon::today();
        $uniqueId = uniqid();

        // Add some test data
        $this->statsService->increment("event.one.{$uniqueId}", 5, $date);
        $this->statsService->increment("event.two.{$uniqueId}", 3, $date);
        $this->statsService->increment("event.three.{$uniqueId}", 1, $date);

        $stats = $this->statsService->getDailyStats($date);

        $this->assertIsArray($stats);
        $this->assertCount(3, $stats);
        $this->assertEquals(5, $stats["event.one.{$uniqueId}"]);
        $this->assertEquals(3, $stats["event.two.{$uniqueId}"]);
        $this->assertEquals(1, $stats["event.three.{$uniqueId}"]);
    }

    public function test_can_aggregate_daily_stats(): void
    {
        $date = Carbon::yesterday();
        $uniqueId = uniqid();

        // Add some test data to Redis
        $this->statsService->increment("event.one.{$uniqueId}", 10, $date);
        $this->statsService->increment("event.two.{$uniqueId}", 5, $date);

        // Aggregate to database
        $aggregatedCount = $this->statsService->aggregateDailyStats($date);

        $this->assertEquals(2, $aggregatedCount);

        // Verify data is in database
        $dbStats = DailyStatistic::where('date', $date->format('Y-m-d'))
            ->where('event_name', 'like', "%.{$uniqueId}")
            ->get();
        $this->assertCount(2, $dbStats);

        $eventOne = $dbStats->where('event_name', "event.one.{$uniqueId}")->first();
        $this->assertNotNull($eventOne);
        $this->assertEquals(10, $eventOne->count);

        $eventTwo = $dbStats->where('event_name', "event.two.{$uniqueId}")->first();
        $this->assertNotNull($eventTwo);
        $this->assertEquals(5, $eventTwo->count);
    }

    public function test_can_clear_redis_stats(): void
    {
        $date = Carbon::yesterday();
        $uniqueId = uniqid();

        // Add some test data
        $this->statsService->increment("event.one.{$uniqueId}", 5, $date);
        $this->statsService->increment("event.two.{$uniqueId}", 3, $date);

        // Verify data exists
        $stats = $this->statsService->getDailyStats($date);
        $this->assertCount(2, $stats);

        // Clear Redis stats
        $clearedCount = $this->statsService->clearDailyRedisStats($date);
        $this->assertEquals(2, $clearedCount);

        // Verify data is cleared
        $stats = $this->statsService->getDailyStats($date);
        $this->assertEmpty($stats);
    }

    public function test_can_get_historical_stats(): void
    {
        $startDate = Carbon::now()->subDays(5);
        $endDate = Carbon::now()->subDays(1);
        $eventName = 'test.historical';

        // Create some historical data
        for ($i = 0; $i < 5; $i++) {
            $date = $startDate->copy()->addDays($i);
            DailyStatistic::create([
                'date' => $date->format('Y-m-d'),
                'event_name' => $eventName,
                'count' => ($i + 1) * 10,
            ]);
        }

        $historicalStats = $this->statsService->getHistoricalStats($eventName, $startDate, $endDate);

        $this->assertCount(5, $historicalStats);
        $this->assertEquals(10, $historicalStats->first()->count);
        $this->assertEquals(50, $historicalStats->last()->count);
    }

    public function test_can_get_total_count(): void
    {
        $startDate = Carbon::now()->subDays(3);
        $endDate = Carbon::now()->subDays(1);
        $eventName = 'test.total';

        // Create some test data
        DailyStatistic::create([
            'date' => $startDate->format('Y-m-d'),
            'event_name' => $eventName,
            'count' => 10,
        ]);

        DailyStatistic::create([
            'date' => $startDate->copy()->addDay()->format('Y-m-d'),
            'event_name' => $eventName,
            'count' => 20,
        ]);

        DailyStatistic::create([
            'date' => $endDate->format('Y-m-d'),
            'event_name' => $eventName,
            'count' => 30,
        ]);

        $totalCount = $this->statsService->getTotalCount($eventName, Carbon::now()->subDays(3), $endDate);

        $this->assertEquals(60, $totalCount);
    }

    public function test_redis_keys_have_ttl(): void
    {
        $eventName = 'test.ttl.' . uniqid();

        $this->statsService->increment($eventName);

        $key = "{$this->testKeyPrefix}:" . Carbon::today()->format('Y-m-d') . ":{$eventName}";
        $ttl = Redis::connection('default')->ttl($key);

        // TTL should be set (greater than 0)
        $this->assertGreaterThan(0, $ttl);
        // Should be approximately 1 day (86400 seconds), allowing reasonable margin
        $this->assertLessThanOrEqual(86400, $ttl);
        $this->assertGreaterThan(80000, $ttl); // Allow more reasonable margin for test environment
    }
}
