# PayU Payment Confirmation Webhook Process Documentation

## Overview

This document provides a comprehensive analysis of the PayU payment provider webhook processing flow in the Laravel application. The system handles payment confirmations, subscription activations, and related business logic through a well-structured webhook processing pipeline.

## 1. Webhook Flow Analysis

### Entry Point: PaymentWebhookController

The webhook process begins at the `PaymentWebhookController` which routes PayU webhooks to the appropriate handler:

```php
// Route: POST /webhook/payments/payu
public function handlePayU(Request $request)
{
    (new PayUPaymentProvider())->handleWebhook($request);
    return response('OK', 200);
}
```

**Route Configuration:**
- URL: `/webhook/payments/{provider}` where provider = 'payu'
- Domain: Configured via `config('app.domain.app')`
- Route Name: `payments-webhook`

### PayU Provider Webhook Handler

The main webhook processing logic is implemented in `PayUPaymentProvider::handleWebhook()`:

```php
public function handleWebhook(Request $request): void
{
    $data = $request->all();
    
    // Validate payload structure
    if (blank($data['order']['orderId'] ?? [])) {
        Log::info('Payment payload incomplete', $data);
        return;
    }
    
    // Find payment record
    $this->payment = Payment::where('provider_payment_id', $data['order']['orderId'])
        ->where('provider', static::class)
        ->first();
        
    // Process webhook...
}
```

## 2. PayU Integration Details

### Configuration

PayU integration is configured via environment variables in `config/services.php`:

```php
'payu' => [
    'pos_id' => env('PAYU_POS_ID'),
    'second_key' => env('PAYU_SECOND_KEY'),
    'client_id' => env('PAYU_CLIENT_ID'),
    'client_secret' => env('PAYU_CLIENT_SECRET'),
    'environment' => env('PAYU_ENV', 'sandbox'),
    'webhook_code' => env('PAYU_WEBHOOK_CODE', 'payu'),
],
```

### PayU SDK Setup

The provider initializes PayU SDK configuration in the constructor:

```php
protected function setupPayU(): void
{
    $config = $this->getPayUConfig();
    
    \OpenPayU_Configuration::setEnvironment($config['environment']);
    \OpenPayU_Configuration::setMerchantPosId($config['pos_id']);
    \OpenPayU_Configuration::setSignatureKey($config['second_key']);
    \OpenPayU_Configuration::setOauthClientId($config['client_id']);
    \OpenPayU_Configuration::setOauthClientSecret($config['client_secret']);
    
    // Setup OAuth token cache
    \OpenPayU_Configuration::setOauthTokenCache(
        new \OauthCacheFile(storage_path('framework/cache/payu'))
    );
}
```

### Payment Creation Process

When creating a payment, the system:

1. Creates a Payment record with status `PaymentStatus::NEW`
2. Builds PayU order data with webhook URL
3. Sends order to PayU API
4. Updates payment with PayU order ID and status

```php
$order['notifyUrl'] = route('payments-webhook', ['provider' => 'payu']);
$order['extOrderId'] = $subscription->getOrderId(); // Unique subscription order ID
```

## 3. Payment Status Handling

### Supported Payment Statuses

The system handles the following PayU payment statuses:

- `COMPLETED` - Payment successful
- `CANCELED` - Payment canceled by user or system
- Other statuses are logged but not specifically processed

### Status Processing Logic

```php
// Handle canceled payments
if ($data['order']['status'] === PaymentStatus::CANCELED->name) {
    $this->payment->update(['status' => $data['order']['status']]);
    PaymentCanceledJob::dispatch($this->payment);
    return;
}

// Handle completed payments
if ($data['order']['status'] === PaymentStatus::COMPLETED->name) {
    // Extract payment transaction ID
    $property = array_filter($data['properties'], fn($p) => $p['name'] === 'PAYMENT_ID');
    $paymentId = !empty($property) ? reset($property)['value'] : null;
    
    // Update payment record
    $this->payment->update([
        'status' => PaymentStatus::COMPLETED,
        'paid_at' => Carbon::parse($data['localReceiptDateTime']),
        'payment_method' => $data['order']['payMethod']['type'] ?? null,
        'payment_transaction_id' => $paymentId,
    ]);
    
    PaymentCompletedJob::dispatch($this->payment);
}
```

## 4. Security Implementation

### Webhook Signature Verification

**Note:** Signature verification is currently commented out but implemented:

```php
// Currently disabled in webhook handler:
// if (!$this->checksum($request)) {
//     Log::error('Payment checksum failed');
//     return;
// }
```

The checksum verification method:

```php
protected function checksum(Request $request): bool
{
    $signatureHeader = $request->header('x-openpayu-signature', '');
    $headers = explode(";", $signatureHeader);
    
    if (blank($headers['signature'])) {
        return false;
    }
    
    return md5($request->getContent() . \OpenPayU_Configuration::getSignatureKey()) 
           === $headers['signature'];
}
```

### Request Validation

The system validates:
- Presence of required webhook data (`order.orderId`)
- Payment record existence in database
- Payment provider match
- Duplicate processing prevention

## 5. Database Operations

### Payment Record Updates

For completed payments:
```php
$this->payment->update([
    'status' => PaymentStatus::COMPLETED,
    'paid_at' => Carbon::parse($data['localReceiptDateTime']),
    'payment_method' => $data['order']['payMethod']['type'] ?? null,
    'payment_transaction_id' => $paymentId,
]);
```

### Webhook Payload Storage

All webhook payloads are stored for audit purposes:

```php
$data['checksum'] = $this->getChecksum($request);

$this->payment->webhooks()->create([
    'payload' => $data
]);
```

**PaymentWebhook Model Structure:**
- `payment_id` - Foreign key to payments table
- `payload` - JSON field storing complete webhook data
- `created_at/updated_at` - Timestamps

### Subscription Status Updates

The subscription status is updated through the `paymentSuccessful()` method:

```php
public function paymentSuccessful(): self
{
    if ($this->plan->type === PlanType::TRIAL) {
        $this->status = SubscriptionStatus::TRIAL;
    } elseif ($this->starts_at->isToday()) {
        $this->status = SubscriptionStatus::ACTIVE;
    } else {
        $this->status = SubscriptionStatus::PENDING;
    }
    
    $this->save();
    return $this;
}
```

## 6. Event System

### Events Dispatched

1. **PaymentCompleted Event** - Dispatched after successful payment processing
2. **PaymentCanceled Event** - Dispatched when payment is canceled

### Event Listeners

**PaymentCompletedListener:**
- Sends in-app notifications to users
- Queues success email notifications
- Differentiates between trial and paid plans

**PaymentCanceledListener:**
- Sends cancellation notifications
- Queues cancellation email notifications

## 7. Subscription Activation

### Tenant Activation Process

When a payment is completed and subscription becomes active:

```php
if ($this->payment->subscription->status === SubscriptionStatus::ACTIVE) {
    $this->payment->tenant->activateSubscription($this->payment->subscription);
}
```

### Tenant Configuration Updates

The `activateSubscription()` method:
1. Cancels any existing subscription
2. Updates tenant configuration with plan features
3. Sets subscription status and selected plan ID
4. Links tenant to the new subscription

```php
public function activateSubscription(Subscription $subscription): Tenant
{
    // Cancel old subscription if exists
    if ($this->subscription_id !== null) {
        $oldsub = Subscription::find($this->subscription_id);
        if ($oldsub) {
            $oldsub->cancel();
        }
    }
    
    // Update tenant configuration
    $tenantConfig = $this->config;
    $tenantConfig['modules'] = $subscription->plan->features ?? [
        SystemModules::INVOICES->value,
        SystemModules::SIMPLE_PRODUCTS->value
    ];
    $tenantConfig['selected_plan_id'] = $subscription->plan->id;
    $tenantConfig['subscription_status'] = $subscription->plan->type === PlanType::TRIAL ?
        SubscriptionStatus::TRIAL->name : SubscriptionStatus::ACTIVE->name;
    
    $this->update([
        'config' => $tenantConfig,
        'subscription_id' => $subscription->id
    ]);
    
    return $this->refresh();
}
```

## 8. Error Handling

### Validation Errors

- **Missing Order ID:** Logged as info, webhook processing stops
- **Payment Not Found:** Logged as error, webhook processing stops  
- **Already Completed:** Logged as info, prevents duplicate processing

### Exception Handling

During payment creation, exceptions are caught and logged:

```php
try {
    $response = \OpenPayU_Order::create($order);
} catch (\Exception $e) {
    Log::error($e->getMessage());
    $this->payment->update([
        'status' => PaymentStatus::ERROR,
        'meta' => ['error' => $e->getMessage()]
    ]);
    return null;
}
```

### Logging Strategy

- Info level: Normal webhook events, incomplete payloads
- Error level: Payment not found, API errors, checksum failures
- All webhook payloads stored in database for debugging

## 9. Dependencies and Services

### Core Models
- `Payment` - Main payment record
- `PaymentWebhook` - Webhook payload storage
- `Subscription` - Subscription management
- `Tenant` - Multi-tenant configuration
- `Plan` - Subscription plan details
- `User` - User management

### Jobs (Queued Processing)
- `PaymentCompletedJob` - Handles successful payment processing
- `PaymentCanceledJob` - Handles payment cancellation

### Repositories
- `SubscriptionsRepository` - Subscription management logic
- `TradeDocsRepository` - Invoice creation for paid subscriptions

### Mail Classes
- `PaymentSuccess` - Success notification with invoice attachment
- `PaymentCanceled` - Cancellation notification

### External Dependencies
- PayU PHP SDK (`OpenPayU_*` classes)
- Laravel Queue system for job processing
- Laravel Mail system for notifications
- Filament notifications for in-app alerts

This comprehensive webhook process ensures reliable payment processing, proper subscription activation, and complete audit trails for all PayU payment transactions.
