# User Registration Process Flow Documentation

## Overview

This document provides a comprehensive analysis of the complete user registration process in the Laravel application, starting from the Filament registration page through successful account creation and tenant setup.

## Registration Flow Architecture

The registration process consists of three main phases:
1. **Initial Registration** - Email and VAT ID collection
2. **Email Confirmation** - 6-digit code verification
3. **Data Collection & Account Creation** - User details, company data, and subscription setup

## Phase 1: Initial Registration

### Entry Point
- **File**: `app/Filament/App/Pages/Auth/Register.php`
- **Route**: Standard Filament registration route
- **View**: Filament-generated registration form

### Form Fields
```php
// Email validation with duplicate checking
TextInput::make('email')
    ->email()
    ->required()
    ->maxLength(255)
    ->rules([
        // Custom validation to check for existing users and registrations
    ])

// VAT ID with real-time formatting
TextInput::make('vat_id')
    ->required()
    ->live(true)
    ->afterStateUpdated() // Formats to 10 digits only
    ->rules([
        // Validates exactly 10 numeric digits
    ])
```

### Validation Logic
- **Email Uniqueness**: Checks against active users and confirmed registrations
- **VAT ID Format**: Automatically extracts and validates 10-digit Polish NIP
- **Existing Registration Cleanup**: Deletes unconfirmed registrations for the same email

### Database Operations
Creates a new `Registration` record with:
```php
Registration::create([
    'email' => $data['email'],
    'vat_id' => $data['vat_id'],
    'registration_hash' => bin2hex(random_bytes(32)), // 64-char unique hash
    'confirmation_code' => Registration::generateConfirmationCode(), // 6-digit code
    'code_sent_at' => now(),
]);
```

### Email Notification
- **Mail Class**: `App\Mail\RegistrationConfirmation`
- **Template**: `resources/views/email/registration-confirmation-html.blade.php`
- **Queue**: Implements `ShouldQueue` for background processing
- **Content**: Contains 6-digit confirmation code and confirmation URL

### Event Dispatching
```php
RegistrationCreated::dispatch($registration);
```

### Redirect
Automatically redirects to confirmation page with registration hash.

## Phase 2: Email Confirmation

### Entry Point
- **File**: `app/Filament/App/Pages/Auth/ConfirmRegistration.php`
- **Route**: `/app/register/confirm/{hash}`
- **Method**: `mount(string $hash)`

### Security Validations
```php
// Registration existence check
$registration = Registration::where('registration_hash', $hash)->first();

// Prevent access to finished registrations
if ($registration->isFinished()) {
    return redirect(Filament::getLoginUrl());
}
```

### Form Validation
- **6-digit code verification** against stored `confirmation_code`
- **Registration state validation** ensures registration exists and is not finished

### Confirmation Process
1. **Mark as Confirmed**: Updates `confirmed_at` timestamp
2. **GUS Integration**: Fetches company data from Polish business registry
3. **Data Storage**: Saves company information to `registration.data` JSON field
4. **Session Management**: Stores confirmation code in session for next step
5. **Event Dispatch**: `RegistrationConfirmed::dispatch($registration)`

### GUS Repository Integration
```php
$company = GUSRepository::findByNip($registration->vat_id);
if (filled($company)) {
    $data = PartnerResource::mapGUSResponseToFormData($company);
    $registration->data = ['company' => $data];
    $registration->save();
}
```

## Phase 3: Data Collection & Account Creation

### Entry Point
- **File**: `app/Filament/App/Pages/Auth/ConfirmRegistrationData.php`
- **Route**: `/app/register/confirm-data/{hash}`
- **Form Type**: Multi-step wizard with 5 steps

### Security Validations
- Registration must be confirmed (`confirmed_at` not null)
- Session must contain valid confirmation code
- Registration must not be finished

### Wizard Steps

#### Step 1: Personal Data (`Dane Osobowe`)
```php
TextInput::make('user.name')->required()->maxLength(100)
TextInput::make('user.surname')->required()->maxLength(255)
TextInput::make('user.password')->required()->minLength(10)->password()
TextInput::make('user.password_confirmation')->same('user.password')
TextInput::make('user.adress')->maxLength(255)
TextInput::make('user.number')->numeric()->maxLength(255)
```

#### Step 2: Accounting Data (`Dane księgowe`)
```php
Select::make('company.tax_residency_country')->required()
TextInput::make('company.vat_id')->required()->live()
Select::make('company.business_type')->required()
Select::make('company.vat_type')->required()
Select::make('company.tax_type')
Select::make('company.accounting_type')
TextInput::make('company.meta.accounting.regon')
TextInput::make('company.meta.accounting.bdo')
```

#### Step 3: Company Data (`Dane Firmy`)
```php
TextArea::make('company.name')->required()->maxLength(120)
TextInput::make('company.postcode')->required()
TextInput::make('company.city')->required()
TextInput::make('company.phone')->numeric()
TextInput::make('company.email')->email()
TextInput::make('company.contact_name')
TextInput::make('company.website')->url()
Textarea::make('company.address')->required()->maxLength(500)
```

#### Step 4: Bank Accounts (`Dane bankowe`)
```php
Repeater::make('company.meta.bank_accounts')
    ->schema([
        TextInput::make('account_name')
        TextInput::make('bank_name')
        TextInput::make('bank_account')->mask('99 9999 9999 9999 9999 9999 9999')
        TextInput::make('bank_swift')
        TextInput::make('bank_iban')
        Select::make('bank_currency')->required()->default('PLN')
    ])
```

#### Step 5: Plan Selection (`Wybór planu`)
```php
Radio::make('selected_plan_id')
    ->required()
    ->options($this->getPlanOptions()) // Active plans with features
    ->descriptions($this->getPlanDescriptions()) // Plan details and features
```

### Account Creation Process

The final submission triggers `buildSystemEntities()` method within a database transaction:

#### 1. Plan Validation
```php
$selectedPlan = Plan::find($selectedPlanId);
if (!$selectedPlan || !$selectedPlan->is_active) {
    throw new \Exception('Invalid plan selected');
}
```

#### 2. Tenant Creation
```php
$tenant = Tenant::create([
    ...Arr::except($companyData, 'meta'),
    'hash' => bin2hex(random_bytes(16)),
    'config' => [
        'modules' => [],
        'selected_plan_id' => $selectedPlanId,
        'subscription_status' => $selectedPlan->type === PlanType::TRIAL ? 
            SubscriptionStatus::TRIAL->name : SubscriptionStatus::NEW->name,
    ],
    'is_active' => true,
]);
```

#### 3. Tenant Metadata Creation
```php
$tenant->meta()->create(Arr::only($companyData, 'meta'));
```

#### 4. User Creation
```php
$user = User::create([
    'name' => $registration->email,
    'email' => $registration->email,
    'password' => Hash::make($userData['password']),
    'active' => true,
]);

$user->assignRole(Roles::TENANT_ADMIN);
```

#### 5. Profile Data Creation
```php
$user->profile()->create([
    ...Arr::except($userData, ['password', 'password_confirmation']),
    'lang' => 'pl'
]);
```

#### 6. User-Tenant Association
```php
$user->tenant()->attach($tenant->id);
```

#### 7. Partner Conversion
```php
$partner = $tenant->convertToPartner(systemTenant()->id, true);
$tenant->update(['system_partner' => $partner->id]);
```

#### 8. Registration Completion
```php
$registration->update(['finished_at' => now()]);
```

### Subscription & Payment Processing

#### Subscription Creation
```php
$subRepo = new SubscriptionsRepository();
$subscription = $subRepo->createSubscriptionWithPayment($user, $selectedPlan);
```

#### Payment Flow
- **Trial Plans**: Processed internally, immediately activated
- **Paid Plans**: Redirected to PayU payment gateway
- **Payment URL**: Stored for potential redirect after login

#### Document Series Setup
```php
DocumentSeriesRepository::seedDefaultSeries($tenant, 'trade');
```

### Final Steps

#### User Authentication
```php
Auth::login($user);
```

#### Event Dispatching
```php
RegistrationFinished::dispatch($registration, $user);
```

#### Welcome Email
Automatically sent via event listener:
```php
Mail::to($user->email)->queue(new WelcomeEmail($user));
```

#### Session Cleanup
```php
Session::forget('registration_code');
```

#### Redirect Logic
- **Pending Payment**: Redirect to payment gateway
- **Completed Registration**: Redirect to login page

## Database Schema

### Tables Created/Modified During Registration

#### `registrations` Table
```sql
- id (bigint, auto-increment)
- email (string, unique)
- vat_id (string, 10 chars)
- confirmation_code (string, 6 chars)
- registration_hash (string, 65 chars)
- code_sent_at (timestamp)
- confirmed_at (timestamp, nullable)
- finished_at (timestamp, nullable)
- data (json, nullable)
```

#### `users` Table
```sql
- id, name, email, password, active
- Related: profile_data table for extended user information
```

#### `tenants` Table
```sql
- Company information, VAT details, configuration
- Related: tenant_metas table for additional metadata
```

#### `subscriptions` Table
```sql
- User-plan association with payment status
- Related: payments table for transaction records
```

#### `installations` Table
```sql
- User-tenant relationship (many-to-many pivot)
```

## Security Considerations

### Data Protection
- **Password Hashing**: Uses Laravel's Hash facade with bcrypt
- **Session Security**: Confirmation codes stored in encrypted sessions
- **Hash Generation**: Cryptographically secure random bytes for hashes

### Access Control
- **Registration Hash Validation**: Prevents unauthorized access to confirmation steps
- **Session Verification**: Confirmation code must match session data
- **State Validation**: Each step validates previous completion

### Input Validation
- **Email Sanitization**: Laravel's email validation rules
- **VAT ID Formatting**: Automatic digit extraction and validation
- **SQL Injection Prevention**: Eloquent ORM with parameter binding

## Error Handling

### Validation Errors
- **Form-level validation** with custom Polish error messages
- **Business logic validation** (duplicate emails, invalid plans)
- **Database constraint validation** with graceful error handling

### Exception Handling
- **Transaction rollback** on any failure during account creation
- **Graceful degradation** with user-friendly error messages
- **Logging** of critical errors for debugging

## Integration Points

### External Services
- **GUS API**: Polish business registry for company data
- **PayU Payment Gateway**: For subscription payments
- **Email Queue System**: For reliable email delivery

### Internal Services
- **Filament Framework**: For form handling and UI
- **Spatie Roles & Permissions**: For user role assignment
- **Laravel Events**: For decoupled notification handling

## Testing Considerations

### Unit Tests
- Registration model validation
- Email confirmation logic
- User creation process

### Integration Tests
- Complete registration flow
- Payment processing
- Email delivery

### Security Tests
- Hash validation
- Session security
- Access control

## Monitoring & Analytics

### Key Metrics
- Registration completion rate
- Email confirmation rate
- Payment success rate
- Time to complete registration

### Error Tracking
- Failed registrations
- Payment failures
- Email delivery issues

This documentation provides a complete overview of the registration process, enabling developers to understand, maintain, and extend the system effectively.
