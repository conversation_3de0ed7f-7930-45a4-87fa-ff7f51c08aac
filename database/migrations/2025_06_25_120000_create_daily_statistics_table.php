<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_statistics', function (Blueprint $table) {
            $table->id();
            $table->date('date')->comment('Date for which the statistics are recorded');
            $table->string('event_name')->comment('Name of the event being tracked');
            $table->unsignedBigInteger('count')->default(0)->comment('Number of occurrences for this event on this date');
            $table->json('metadata')->nullable()->comment('Additional metadata for the statistic');
            $table->timestamps();

            // Indexes for performance
            $table->unique(['date', 'event_name'], 'daily_stats_date_event_unique');
            $table->index('date');
            $table->index('event_name');
            $table->index(['event_name', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_statistics');
    }
};
