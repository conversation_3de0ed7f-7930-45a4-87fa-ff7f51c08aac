<?php

namespace Database\Factories;

use App\Models\Registration;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Registration>
 */
class RegistrationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Registration::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'vat_id' => $this->faker->numerify('##########'), // 10 digits
            'confirmation_code' => Registration::generateConfirmationCode(),
            'registration_hash' => hash('sha256', $this->faker->uuid() . time()),
            'code_sent_at' => now(),
            'confirmed_at' => null,
            'finished_at' => null,
            'data' => [
                'company_name' => $this->faker->company(),
                'address' => $this->faker->address(),
                'phone' => $this->faker->phoneNumber(),
            ],
        ];
    }

    /**
     * Indicate that the registration is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Indicate that the registration is finished.
     */
    public function finished(): static
    {
        return $this->state(fn (array $attributes) => [
            'confirmed_at' => now()->subHour(),
            'finished_at' => now(),
        ]);
    }

    /**
     * Indicate that the registration is old and unconfirmed (uses config values).
     */
    public function oldUnconfirmed(): static
    {
        $interval = new \DateInterval(config('app.registration.created_valid_for'));
        return $this->state(fn (array $attributes) => [
            'confirmed_at' => null,
            'finished_at' => null,
            'created_at' => now()->sub($interval)->subMinute(),
        ]);
    }

    /**
     * Indicate that the registration is old, confirmed but not finished (uses config values).
     */
    public function oldConfirmedNotFinished(): static
    {
        $interval = new \DateInterval(config('app.registration.confirmed_valid_for'));
        return $this->state(fn (array $attributes) => [
            'confirmed_at' => now()->sub($interval)->subMinute(),
            'finished_at' => null,
        ]);
    }

    /**
     * Indicate that the registration is old and finished (uses config values).
     */
    public function oldFinished(): static
    {
        $interval = new \DateInterval(config('app.registration.finished_valid_for'));
        $oldTime = now()->sub($interval)->subHour();
        return $this->state(fn (array $attributes) => [
            'confirmed_at' => $oldTime->copy()->subHour(),
            'finished_at' => $oldTime,
        ]);
    }
}
