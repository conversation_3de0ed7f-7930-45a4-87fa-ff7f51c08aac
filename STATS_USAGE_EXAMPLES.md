# Statistics Service Usage Examples

This document provides examples of how to use the comprehensive statistics service.

## Basic Usage

### Using the Facade (Recommended)

```php
use App\Facades\Stats;

// Increment a counter
Stats::increment('user.login');
Stats::increment('page.view', 5); // Increment by 5
Stats::increment('api.request', 1, Carbon::yesterday()); // For specific date

// Get current count
$loginCount = Stats::getCount('user.login');
$yesterdayViews = Stats::getCount('page.view', Carbon::yesterday());

// Get all stats for today
$todayStats = Stats::getDailyStats();
// Returns: ['user.login' => 10, 'page.view' => 150, ...]

// Get historical data from database
$weeklyLogins = Stats::getHistoricalStats('user.login', Carbon::now()->subWeek(), Carbon::now());
$totalLogins = Stats::getTotalCount('user.login', Carbon::now()->subMonth(), Carbon::now());
```

### Using the Service Directly

```php
use App\Services\StatsService;

$statsService = app(StatsService::class);
$statsService->increment('order.created');
```

## Common Use Cases

### 1. Track User Activity

```php
// In your authentication controller
Stats::increment('user.login');
Stats::increment('user.logout');

// In your registration controller
Stats::increment('user.register');
```

### 2. Track API Usage

```php
// In your API middleware
Stats::increment('api.request');
Stats::increment('api.request.' . $request->route()->getName());
```

### 3. Track Business Events

```php
// When an order is created
Stats::increment('order.created');
Stats::increment('revenue.generated', $order->total_cents);

// When a payment is completed
Stats::increment('payment.completed');
Stats::increment('payment.method.' . $payment->method);
```

### 4. Track Content Engagement

```php
// Page views
Stats::increment('page.view');
Stats::increment('page.view.' . $page->slug);

// File downloads
Stats::increment('file.download');
Stats::increment('file.download.' . $file->type);
```

## Advanced Usage

### Custom Date Tracking

```php
// Track events for specific dates
$customDate = Carbon::createFromFormat('Y-m-d', '2025-06-20');
Stats::increment('special.event', 1, $customDate);
```

### Batch Operations

```php
// Track multiple events
Stats::increment('email.sent');
Stats::increment('email.sent.newsletter');
Stats::increment('email.sent.user.' . $user->id);
```

### Getting Analytics Data

```php
// Get last 30 days of login data
$startDate = Carbon::now()->subDays(30);
$endDate = Carbon::now();
$loginStats = Stats::getHistoricalStats('user.login', $startDate, $endDate);

// Calculate total API requests this month
$monthStart = Carbon::now()->startOfMonth();
$totalApiRequests = Stats::getTotalCount('api.request', $monthStart, Carbon::now());

// Get yesterday's complete statistics
$yesterdayStats = Stats::getDailyStats(Carbon::yesterday());
```

## Management Commands

### Daily Aggregation

```bash
# Aggregate yesterday's stats (default)
./server artisan stats:aggregate

# Aggregate specific date
./server artisan stats:aggregate 2025-06-24

# Use queue for aggregation
./server artisan stats:aggregate --queue

# Don't clear Redis after aggregation
./server artisan stats:aggregate --no-clear

# Force aggregation even if data exists
./server artisan stats:aggregate --force
```

### Scheduled Aggregation

The service automatically schedules daily aggregation at 1:00 AM via Laravel's task scheduler. Make sure your cron is configured:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Redis Configuration
STATS_REDIS_CONNECTION=default
STATS_REDIS_PREFIX=stats
STATS_REDIS_TTL_DAYS=7

# Aggregation Configuration
STATS_AGGREGATION_ENABLED=true
STATS_AGGREGATION_QUEUE=default
STATS_AGGREGATION_BATCH_SIZE=1000
STATS_CLEAR_REDIS_AFTER_AGGREGATION=true

# Logging Configuration
STATS_LOGGING_ENABLED=true
STATS_LOG_CHANNEL=single
STATS_LOG_LEVEL=info
```

## Event Naming Conventions

Use descriptive, hierarchical event names:

```php
// Good examples
Stats::increment('user.login');
Stats::increment('user.login.success');
Stats::increment('user.login.failed');
Stats::increment('api.request.v1.users');
Stats::increment('email.sent.welcome');
Stats::increment('payment.completed.stripe');

// Avoid
Stats::increment('login'); // Too generic
Stats::increment('user_login_success'); // Use dots, not underscores
```

## Performance Considerations

1. **Redis Keys**: Keys are automatically set with TTL to prevent indefinite storage
2. **Batch Processing**: Use the queue option for aggregation in production
3. **Event Names**: Keep event names concise but descriptive
4. **Monitoring**: Enable logging to monitor service performance

## Error Handling

The service includes comprehensive error handling and logging:

```php
try {
    Stats::increment('risky.operation');
} catch (\Exception $e) {
    // Service will log errors automatically
    // Your application continues normally
}
```

## Integration with Existing Code

### In Controllers

```php
class OrderController extends Controller
{
    public function store(Request $request)
    {
        $order = Order::create($request->validated());
        
        // Track the event
        Stats::increment('order.created');
        Stats::increment('order.created.user.' . auth()->id());
        
        return response()->json($order);
    }
}
```

### In Jobs

```php
class ProcessPaymentJob implements ShouldQueue
{
    public function handle()
    {
        // Process payment logic...
        
        Stats::increment('payment.processed');
        
        if ($paymentSuccessful) {
            Stats::increment('payment.success');
        } else {
            Stats::increment('payment.failed');
        }
    }
}
```

### In Middleware

```php
class TrackApiUsage
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        Stats::increment('api.request');
        Stats::increment('api.response.' . $response->getStatusCode());
        
        return $response;
    }
}
```
